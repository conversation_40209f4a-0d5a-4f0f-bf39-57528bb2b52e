package inks.service.sa.uts.utils.wxutils; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/7
 * @param 企业微信通讯录类
 */

public class QyWeChat {
    //获取部门列表接口
    public static String getDepartmentList = "https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token={access_token}";
    //根据部门获取成员简单list
    public static String getSimpleList = "https://qyapi.weixin.qq.com/cgi-bin/user/simplelist?access_token={access_token}&department_id={department_id}";
    //发送信息接口 官方文档：https://developer.work.weixin.qq.com/document/path/90236#markdown%E6%B6%88%E6%81%AF
    public static String message = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}";
    //获取token接口
    public static String getToken = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}";
    //提交审批申请
    public static String approveURL = "https://qyapi.weixin.qq.com/cgi-bin/oa/applyevent?access_token={access_token}";

    //获取审批模板详情 nanno 20230218
    public static String templateDetailURL = "https://qyapi.weixin.qq.com/cgi-bin/oa/gettemplatedetail?access_token={access_token}";
    /**
     * 企业ID（yes）+
     */
    public static String corpId = "";

    /**
     * 发送信息密钥
     */
    public static String mailSecret = "PskayCTne6DGyhSz-7oMAXvzvo8HCnctaRK86vM1BRs";
    //第三方应用凭证密钥(yes)
    public static String agentSecret = "OzpFaBLxEvnoz6o7hnQMEeUq42gS19WvXl-QC6_Lbb0";
    //审批密钥
    public static String approve = "ARN_KyAou08ZaloWGkg8ZvtQFj2w8MBgJtCPF3552AM";

    /**
     * 企业应用的id，整型。可在应用的设置页面查看(yes)项目测试（ebo0.2版本）
     */
    public static int agentId = 1000006;

    /**
     * 应用secret
     */
    public static String secret = "**********";
    //回调token
    public static String callbacktoken = "";
    //回调密钥
    public static String callbackAESKey = "";
}
