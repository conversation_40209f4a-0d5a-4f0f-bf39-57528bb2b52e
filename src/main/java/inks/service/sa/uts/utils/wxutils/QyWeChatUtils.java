package inks.service.sa.uts.utils.wxutils; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/13
 * @param
 */

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QyWeChatUtils {
    private final static Logger log = LoggerFactory.getLogger(QyWeChatUtils.class);

    public static String refreshToken(String type) {
        String token = "";
        if ("emaiToken".equals(type)) {
            String url = QyWeChat.getToken.replace("{corpid}", QyWeChat.corpId).replace("{corpsecret}", QyWeChat.mailSecret);
            JSONObject jsonObject = HttpRequestUtil.sendGet(url);
            if (null != jsonObject) {
                token = jsonObject.getString("access_token");
            }
        }
        if ("agentToken".equals(type)) {
            String url = QyWeChat.getToken.replace("{corpid}", QyWeChat.corpId).replace("{corpsecret}", QyWeChat.agentSecret);
            JSONObject jsonObject = HttpRequestUtil.sendGet(url);
            if (null != jsonObject) {
                token = jsonObject.getString("access_token");
                log.info("企业微信access_token:" + token);
            }
        }
        if ("approve".equals(type)) {
            String url = QyWeChat.getToken.replace("{corpid}", QyWeChat.corpId).replace("{corpsecret}", QyWeChat.approve);
            JSONObject jsonObject = HttpRequestUtil.sendGet(url);
            if (null != jsonObject) {
                token = jsonObject.getString("access_token");
            }
        }
        return token;
    }
}
