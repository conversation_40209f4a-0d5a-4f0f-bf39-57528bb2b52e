package inks.service.sa.uts.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zaxxer.hikari.HikariDataSource;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.config.constant.JdbcConstants;
import inks.service.sa.uts.config.constant.MyConstant;
import inks.service.sa.uts.domain.UtsDatabaseEntity;
import inks.service.sa.uts.domain.database.*;
import inks.service.sa.uts.domain.pojo.UtsDatabasePojo;
import inks.service.sa.uts.mapper.UtsDatabaseMapper;
import inks.service.sa.uts.service.JdbcService;
import inks.service.sa.uts.service.UtsDatabaseService;
import inks.service.sa.uts.utils.PrintColor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * 数据库连接池(UtsDatabase)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-11 15:28:21
 */
@Service("utsDatabaseService")
public class UtsDatabaseServiceImpl implements UtsDatabaseService {
    private final static Logger log = LoggerFactory.getLogger(UtsDatabaseServiceImpl.class);
    @Resource
    private UtsDatabaseMapper utsDatabaseMapper;
    @Autowired
    private JdbcService jdbcService;
    @Resource(name = "dataSourceRestTemplate")
    private RestTemplate restTemplate;

    // 本机数据库连接信息:
    @Value("${spring.datasource.url}")
    private String LocalUrl;
    @Value("${spring.datasource.username}")
    private String LocalUsername;
    @Value("${spring.datasource.password}")
    private String LocalPassword;
    @Value("${spring.datasource.driver-class-name}")
    private String LocalDriverClassName;
    private final static Logger logger = LoggerFactory.getLogger(UtsDatabaseServiceImpl.class);


    // 创建一个HashMap来存储每个数据库URL对应的HikariDataSource实例
    // 对于dataSources这个字段来说，我们确实不希望它被更改。我们只希望在dataSources这个HashMap中添加或删除元素，
    // 而不是将dataSources本身指向另一个HashMap。因此，我们可以将dataSources声明为final
    // String存储的是数据库URL，HikariDataSource存储的是数据库连接池
    private final HashMap<String, HikariDataSource> dataSources = new HashMap<>();

    // 当Spring容器中的Bean被销毁前，会自动调用被@PreDestroy注解修饰的方法 用于做一些[清理工作]，如关闭数据库连接、停止后台线程、释放资源等。
    @PreDestroy
    public void preDestroy() {
        for (HikariDataSource dataSource : dataSources.values()) {
            if (dataSource != null && !dataSource.isClosed()) {//避免尝试关闭已经被关闭的数据源
                dataSource.close();
            }
        }
    }


    @Override
    public Map<String, Object> getDataSource(String databaseid, String tid) {
        return getDataSource(databaseid, false, tid, null, null, null, null);
    }

    // 通过Sa_Database.id获取数据库连接dataSource和数据库名字,
    // databaseid若为空null 则默认使用本地数据库连接信息
    // 如果传入的delete为true，先删除缓存的数据库连接池，再重新连接！！
    @Override
    public Map<String, Object> getDataSource(String databaseid, boolean delete, String tid, String url_pre, String username_pre, String password_pre, String drivename_pre) {
        //当前传入连接参数时，才走_pre
        // 默认本地数据库连接信息
        String url = LocalUrl;
        String username = LocalUsername;
        String password = LocalPassword;
        String driverClassName = LocalDriverClassName;
        if (StringUtils.isNotBlank(databaseid)) {
            UtsDatabasePojo databasePojo = utsDatabaseMapper.getEntity(databaseid, tid);
            if (databasePojo == null) {
                throw new BaseBusinessException("Databaseid数据库连接不存在");
            }
            url = databasePojo.getUrl();
            username = databasePojo.getUsername();
            password = databasePojo.getPassword();
            driverClassName = databasePojo.getDriverclassname();
        }
        if (StringUtils.isNoneBlank(url_pre, username_pre, password_pre)) {
            // 3个参数都有值，才执行这里
            url = url_pre;
            username = username_pre;
            password = password_pre;
            driverClassName = drivename_pre;
        }

        // 20250303 如果传入的delete为true，先删除缓存的数据库连接池，再重新连接！！
        if (delete) {
            HikariDataSource dataSource = dataSources.remove(url);
            if (dataSource != null) {
                logger.info("删除缓存的数据库连接池:{}", url);
                dataSource.close(); // 关闭连接池，释放资源
            }
        }
        // 检查我们是否已经为这个数据库URL创建了一个HikariDataSource实例
        HikariDataSource dataSource = dataSources.get(url);
        if (dataSource != null) {
            logger.info("使用缓存数据源: {}", url);
        } else {
            logger.info("创建新的数据源: {}", url);
            dataSource = new HikariDataSource();
            dataSource.setJdbcUrl(url);
            dataSource.setUsername(username);
            dataSource.setPassword(password);
            // 驱动和测试查询逻辑...
            if (StringUtils.isNotBlank(driverClassName)) {
                dataSource.setDriverClassName(driverClassName);
                // 设置连接测试查询 sqlserver连接用jtds老版本驱动只能用这个 (耗时)
                if (driverClassName.contains("jtds")) {
                    //dataSource.setConnectionTestQuery(null); // 错误：禁用测试查询会导致 HikariCP 调用 isValid()
                    dataSource.setConnectionTestQuery("SELECT 1"); // jTDS 1.3.1 不兼容 JDBC 4 的 isValid()，必须指定测试查询
                }
            } else {
                if (url.contains(MyConstant.MYSQL)) {
                    dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
                } else if (url.contains(MyConstant.SQLSERVER)) {
                    dataSource.setDriverClassName("net.sourceforge.jtds.jdbc.Driver");
                    dataSource.setConnectionTestQuery("SELECT 1");
                } else {
                    throw new BaseBusinessException("url中不包含mysql或sqlserver,则必须指定驱动");
                }
            }
            //dataSource.setConnectionTestQuery(null);

            // 将新创建的HikariDataSource实例保存到HashMap中
            dataSources.put(url, dataSource);
        }
        String databaseName = "";
        //String actualCatalog = connection.getCatalog(); // 有connention的话可以直接获取实际连接的数据库名
        // 获取数据库名字 格式为***************************************** 或 **********************************************
        // 优化获取数据库名字，如果有？就截取？和前一个/之间的内容; 否则截取最后一个/之后的内容
        if (url.contains("?")) {
            // 如果 URL 中包含 ?，且包含查询参数
            int indexQuestionMark = url.indexOf('?');
            int indexLastSlash = url.lastIndexOf('/', indexQuestionMark);

            if (indexLastSlash != -1 && indexQuestionMark != -1) {
                // 从最后一个斜杠和 ? 之间的部分截取数据库名
                databaseName = url.substring(indexLastSlash + 1, indexQuestionMark);
            }


        } else if (url.contains("databaseName="))// 如果 URL 中有 'databaseName=' 参数，提取它 *********************************************************************************************;
        {
            int dbNameStartIndex = url.indexOf("databaseName=") + "databaseName=".length();
            int dbNameEndIndex = url.indexOf(';', dbNameStartIndex);
            if (dbNameEndIndex == -1) {
                dbNameEndIndex = url.length(); // 如果没有 ';'，取到字符串末尾
            }
            databaseName = url.substring(dbNameStartIndex, dbNameEndIndex);
        } else {
            // 如果 URL 中不包含 ?，则取最后一个 / 后的部分作为数据库名
            int indexLastSlash = url.lastIndexOf('/');
            if (indexLastSlash != -1) {
                databaseName = url.substring(indexLastSlash + 1);
            }
        }

        PrintColor.red("databaseName=" + databaseName);
        HashMap<String, Object> map = new HashMap<>();
        map.put("dataSource", dataSource);
        map.put("databaseName", databaseName);
        // 数据库类型:url包含mysql就是mysql数据库,包含sqlserver就是sqlserver数据库 默认mysql
        String databaseType = MyConstant.MYSQL;
        if (url.contains(MyConstant.SQLSERVER)) {
            databaseType = MyConstant.SQLSERVER;
        }
        map.put("databaseType", databaseType);
        return map;
    }


    @Override
    public Map<String, String> getLocalSourceConfig() {
        HashMap<String, String> localSourceConfig = new HashMap<>();
        localSourceConfig.put("jdbcUrl", LocalUrl);
        localSourceConfig.put("username", LocalUsername);
        localSourceConfig.put("password", LocalPassword);
        localSourceConfig.put("driverName", LocalDriverClassName);
        return localSourceConfig;
    }

    @Override
    public String testConnection(String databaseid, String tid) {
        // 获取数据库连接和数据库名字 (删缓存连接，再重连)
        Map<String, Object> dataSourceAndName = getDataSource(databaseid, true, tid, null, null, null, null);
        DataSource dataSource = (DataSource) dataSourceAndName.get("dataSource");
        String databaseName = (String) dataSourceAndName.get("databaseName");
        String databaseType = (String) dataSourceAndName.get("databaseType");

        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);

        // 简单的查询SQL，用来测试连接是否成功
        String sql = "SELECT 1";  // 这是一个简单的查询，MySQL 和 SQL Server 都支持
        try {
            // 执行查询，成功则返回连接成功信息
            Integer i = jdbcTemplate.queryForObject(sql, new MapSqlParameterSource(), Integer.class);
            return "测试连接成功：" + databaseName;
        } catch (Exception e) {
            // 如果查询失败，捕获异常并返回失败信息
            return "测试连接失败: " + e.getMessage();
        }
    }

    @Override
    public String testConnectionBackup(String url, String username, String password, String driverclassname, String tenantid) {
        // 获取数据库连接和数据库名字 (删缓存连接，再重连)
        Map<String, Object> dataSourceAndName = getDataSource(null, true, tenantid, url, username, password, driverclassname);
        DataSource dataSource = (DataSource) dataSourceAndName.get("dataSource");
        String databaseName = (String) dataSourceAndName.get("databaseName");
        String databaseType = (String) dataSourceAndName.get("databaseType");

        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);

        // 简单的查询SQL，用来测试连接是否成功
        String sql = "SELECT 1";  // 这是一个简单的查询，MySQL 和 SQL Server 都支持
        try {
            // 执行查询，成功则返回连接成功信息
            Integer i = jdbcTemplate.queryForObject(sql, new MapSqlParameterSource(), Integer.class);
            return "测试连接成功：" + databaseName;
        } catch (Exception e) {
            // 如果查询失败，捕获异常并返回失败信息
            return "测试连接失败: " + e.getMessage();
        }
    }

    //获取Url数据库下所有数据表 databaseid:数据库连接id
    @Override
    public List<Map<String, Object>> table(String databaseid, String tableName, boolean itemFirst, String tid) {
        // 获取数据库连接和数据库名字
        Map<String, Object> dataSourceAndName = getDataSource(databaseid, tid);
        DataSource dataSource = (DataSource) dataSourceAndName.get("dataSource");
        String databaseName = (String) dataSourceAndName.get("databaseName");
        String databaseType = (String) dataSourceAndName.get("databaseType");
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);

        // 构建查询参数
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("database", databaseName);
        if (tableName != null) {
            parameters.addValue("tablename", "%" + tableName.toLowerCase() + "%");
        }
        // 构建查询语句
        String sql = "";
        if (Objects.equals(databaseType, MyConstant.MYSQL)) {
            sql = "SELECT distinct t1.table_name AS tablename, " +
                    "CASE WHEN t2.table_name IS NULL THEN NULL " +
                    "ELSE CONCAT(t2.table_name) END AS tableitemname, " +
                    "t1.table_comment AS tablecomment, " +
                    "t1.create_time AS createtime, " +
                    "t1.update_time AS updatetime " +
                    "FROM information_schema.TABLES t1 " +
                    "LEFT JOIN information_schema.TABLES t2 ON CONCAT(t1.table_name, 'Item') = t2.table_name " +
                    "WHERE t1.table_schema = :database "; //   不展示Item表的话加上:"AND t1.table_name NOT LIKE '%Item' ";

            if (tableName != null) {
                sql += "AND LOWER(t1.table_name) LIKE :tablename ";
            }
            // 如果itemFirst为true，则将Item表排在前面
            if (itemFirst) {
                sql += "ORDER BY IF(t1.table_name LIKE '%Item', 0, 1), t1.table_name";
            }
        } else if (Objects.equals(databaseType, MyConstant.SQLSERVER)) {
            sql = "SELECT TABLE_NAME AS tablename " +
                    "FROM INFORMATION_SCHEMA.TABLES " +
                    "WHERE TABLE_CATALOG = '" + databaseName + "' ";
            if (tableName != null) {
                sql += "AND (TABLE_NAME LIKE '%" + tableName + "%' OR '" + tableName + "' IS NULL) ";
            }
            if (itemFirst) {
                sql += "ORDER BY CASE WHEN 'true' = 'true' AND TABLE_NAME LIKE '%Item' THEN 0 ELSE 1 END, TABLE_NAME";
            }
        }

        // 执行查询并返回结果
        return jdbcTemplate.queryForList(sql, parameters);
    }


    //获取一个数据表所有字段信息 传入数据库连接id和数据表的名字，返回该表所有字段以及字段对应的注释和类型 tableNamePrefix:是否需要表名前缀 默认false
    @Override
    public List<Map<String, Object>> tableFields(String databaseid, String tableName, boolean tableNamePrefix, String tid) {
        // 获取数据库连接和数据库名字
        Map<String, Object> dataSourceAndName = getDataSource(databaseid, tid);
        DataSource dataSource = (DataSource) dataSourceAndName.get("dataSource");
        String databaseName = (String) dataSourceAndName.get("databaseName");
        String databaseType = (String) dataSourceAndName.get("databaseType");
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);

//        String sql = "SELECT column_name AS fieldName, column_comment AS fieldComment, column_type AS fieldType " +
//                "FROM information_schema.COLUMNS WHERE table_schema = :database AND table_name = :tableName";
//        // 默认按照字段名升序,改为排序按照表中字段的定义顺序返回结果:
//        sql += " ORDER BY ORDINAL_POSITION";

        String sql = "";
        if (Objects.equals(databaseType, MyConstant.MYSQL)) {
            sql = "SELECT column_name AS fieldName, column_comment AS fieldComment, column_type AS fieldType " +
                    "FROM information_schema.COLUMNS WHERE table_schema = :database AND table_name = :tableName";
            // 默认按照字段名升序,改为排序按照表中字段的定义顺序返回结果:
            sql += " ORDER BY ORDINAL_POSITION";
        } else if (Objects.equals(databaseType, MyConstant.SQLSERVER)) {
            sql = "SELECT COLUMN_NAME AS fieldName, " +
                    "(SELECT value FROM sys.extended_properties WHERE major_id = OBJECT_ID(:tableName) AND minor_id = COLUMNPROPERTY(major_id, COLUMN_NAME, 'ColumnId') AND name = 'MS_Description') AS fieldComment, " +
                    "DATA_TYPE AS fieldType " +
                    "FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_CATALOG = :database AND TABLE_NAME = :tableName";
            sql += " ORDER BY ORDINAL_POSITION";
        }

        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("database", databaseName);
        params.addValue("tableName", tableName);
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, params);
        // 加个返回列:将数据库类型转换为Java类型
        for (Map<String, Object> map : result) {
            String fieldType = (String) map.get("fieldType");
            String javaType = convertToJavaType(fieldType);
            map.put("javaType", javaType);
            // 如果注释为空，则用字段名代替
            String fieldComment = (String) map.get("fieldComment");
            if (fieldComment == null) {
                fieldComment = (String) map.get("fieldName");
            }
            map.put("fieldComment", fieldComment);
            // fieldName是否需要表名前缀
            if (tableNamePrefix) {
                String fieldName = (String) map.get("fieldName");
                map.put("fieldName", tableName + "." + fieldName);
            }

        }
        return result;
    }


    //   数据库字段类型转换为java类型 规则照抄-其他设置-EasyCode-Type Mapper
// 使用正则表达式去除字段类型中的长度信息 例如，如果fieldType的值是"VARCHAR(255)"，那么fieldType.replaceAll("\\(.*\\)", "").toLowerCase()的结果就是"varchar"。
    private String convertToJavaType(String fieldType) {
        String javaType;
        String typeWithoutLength = fieldType.replaceAll("\\(.*\\)", "").toLowerCase();
        switch (typeWithoutLength) {
            case "varchar":
            case "nvarchar":
            case "char":
            case "text":
            case "json":
                javaType = "String";
                break;
            case "decimal":
            case "real":
            case "double":
                javaType = "Double";
                break;
            case "integer":
            case "int":
            case "int4":
                javaType = "Integer";
                break;
            case "int8":
            case "bigint":
                javaType = "Long";
                break;
            case "date":
            case "datetime":
            case "timestamp":
                javaType = "Date";
                break;
            case "time":
                javaType = "LocalTime";
                break;
            case "boolean":
                javaType = "Boolean";
                break;
            case "blob":
                javaType = "Character";
                break;
            default:
                javaType = fieldType;// 未知类型不做处理
        }
        return javaType;
    }


    @Override
    public UtsDatabasePojo getEntity(String key, String tid) {
        return this.utsDatabaseMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<UtsDatabasePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsDatabasePojo> lst = utsDatabaseMapper.getPageList(queryParam);
            PageInfo<UtsDatabasePojo> pageInfo = new PageInfo<UtsDatabasePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public UtsDatabasePojo insert(UtsDatabasePojo utsDatabasePojo) {
        //初始化NULL字段
        cleanNull(utsDatabasePojo);
        UtsDatabaseEntity utsDatabaseEntity = new UtsDatabaseEntity();
        BeanUtils.copyProperties(utsDatabasePojo, utsDatabaseEntity);
        //生成雪花id
        utsDatabaseEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        utsDatabaseEntity.setRevision(1);  //乐观锁
        this.utsDatabaseMapper.insert(utsDatabaseEntity);
        return this.getEntity(utsDatabaseEntity.getId(), utsDatabaseEntity.getTenantid());
    }


    @Override
    public UtsDatabasePojo update(UtsDatabasePojo utsDatabasePojo) {
        UtsDatabaseEntity utsDatabaseEntity = new UtsDatabaseEntity();
        BeanUtils.copyProperties(utsDatabasePojo, utsDatabaseEntity);
        this.utsDatabaseMapper.update(utsDatabaseEntity);
        return this.getEntity(utsDatabaseEntity.getId(), utsDatabaseEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.utsDatabaseMapper.delete(key, tid);
    }

    @Override
    public String getUrlDatabaseType(String databaseid, String tid) {
        return this.utsDatabaseMapper.getUrlDatabaseType(databaseid, tid);
    }

    private static void cleanNull(UtsDatabasePojo utsDatabasePojo) {
        if (utsDatabasePojo.getTitle() == null) utsDatabasePojo.setTitle("");
        if (utsDatabasePojo.getUrl() == null) utsDatabasePojo.setUrl("");
        if (utsDatabasePojo.getUsername() == null) utsDatabasePojo.setUsername("");
        if (utsDatabasePojo.getPassword() == null) utsDatabasePojo.setPassword("");
        if (utsDatabasePojo.getDriverclassname() == null) utsDatabasePojo.setDriverclassname("");
        if (utsDatabasePojo.getEnabledmark() == null) utsDatabasePojo.setEnabledmark(0);
        if (utsDatabasePojo.getRownum() == null) utsDatabasePojo.setRownum(0);
        if (utsDatabasePojo.getRemark() == null) utsDatabasePojo.setRemark("");
        if (utsDatabasePojo.getCreateby() == null) utsDatabasePojo.setCreateby("");
        if (utsDatabasePojo.getCreatebyid() == null) utsDatabasePojo.setCreatebyid("");
        if (utsDatabasePojo.getCreatedate() == null) utsDatabasePojo.setCreatedate(new Date());
        if (utsDatabasePojo.getLister() == null) utsDatabasePojo.setLister("");
        if (utsDatabasePojo.getListerid() == null) utsDatabasePojo.setListerid("");
        if (utsDatabasePojo.getModifydate() == null) utsDatabasePojo.setModifydate(new Date());
        if (utsDatabasePojo.getTenantid() == null) utsDatabasePojo.setTenantid("");
        if (utsDatabasePojo.getTenantname() == null) utsDatabasePojo.setTenantname("");
        if (utsDatabasePojo.getRevision() == null) utsDatabasePojo.setRevision(0);
    }

    @Override
    public List<JSONObject> execute(DataSourceDto dto) {
        String sourceType = dto.getSourcetype();
        switch (sourceType) {
            // es类型
            case JdbcConstants.ELASTIC_SEARCH_SQL:
                return executeElasticsearchSql(dto);
            // 各种关系型数据库
            case JdbcConstants.MYSQL:
            case JdbcConstants.KUDU_IMAPLA:
            case JdbcConstants.ORACLE:
            case JdbcConstants.SQL_SERVER:
            case JdbcConstants.JDBC:
            case JdbcConstants.POSTGRESQL:
                return executeRelationalDb(dto);
            // http类型
            case JdbcConstants.HTTP:
                return executeHttp(dto);
            default:
                throw new BaseBusinessException("不支持的数据库类型");
        }
    }


    public List<JSONObject> executeRelationalDb(DataSourceDto dto) {
        analysisRelationalDbConfig(dto);
        // 完整的SQL
        String dynSentence = dto.getDynSentence();
        PrintColor.zi("执行SQL语句:" + dynSentence);
        // 校验SQL 必须是查询语句
        JdbcServiceImpl.validateSqlSelect(dynSentence);

        try (Connection pooledConnection = jdbcService.getPooledConnection(dto.getId());
             PreparedStatement statement = pooledConnection.prepareStatement(dynSentence);
             ResultSet rs = statement.executeQuery()) {

            int columnCount = rs.getMetaData().getColumnCount();

            List<String> columns = new ArrayList<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rs.getMetaData().getColumnLabel(i);
                columns.add(columnName);
            }

            List<JSONObject> list = new ArrayList<>();
            while (rs.next()) {
                JSONObject jo = new JSONObject();
                columns.forEach(t -> {
                    try {
                        Object value = rs.getObject(t);
                        // 数据类型转换
                        Object result = dealResult(value);
                        jo.put(t, result);
                    } catch (SQLException throwable) {
                        log.error("error", throwable);
                        throw BusinessExceptionBuilder.build(ResponseCode.EXECUTE_SQL_ERROR, throwable.getMessage());
                    }
                });
                list.add(jo);
            }
            return list;
        } catch (Exception throwable) {
            log.error("error", throwable);
            throw BusinessExceptionBuilder.build(ResponseCode.EXECUTE_SQL_ERROR, throwable.getMessage());
        }
    }

    public void analysisRelationalDbConfig(DataSourceDto dto) {
        JSONObject json = JSONObject.parseObject(dto.getSourceconfig());
        GaeaAssert.isFalse(json.containsKey("jdbcUrl"), ResponseCode.PARAM_IS_NULL, "jdbcUrl not empty");
        GaeaAssert.isFalse(json.containsKey("driverName"), ResponseCode.PARAM_IS_NULL, "driverName not empty");
        String jdbcUrl = json.getString("jdbcUrl");
        String username = json.getString("username");
        String password = json.getString("password");
        String driverName = json.getString("driverName");
        dto.setJdbcUrl(jdbcUrl);
        dto.setDriverName(driverName);
        dto.setUsername(username);
        dto.setPassword(password);
    }

    /**
     * 解决sql返回值 类型问题
     * (through reference chain: java.util.HashMap["pageData"]->java.util.ArrayList[0]->java.util.HashMap["UPDATE_TIME"]->oracle.sql.TIMESTAMP["stream"])
     *
     * @param result
     * @return
     * @throws SQLException
     */
    private Object dealResult(Object result) throws SQLException {
        if (null == result) {
            return result;
        }
        String type = result.getClass().getName();
        if ("oracle.sql.TIMESTAMP".equals(type)) {
            //oracle.sql.TIMESTAMP处理逻辑
            return new Date((Long) JSONObject.toJSON(result));
        }

        return result;
    }


    /**
     * http 执行获取数据
     *
     * @param dto
     */
    public List<JSONObject> executeHttp(DataSourceDto dto) {
        analysisHttpConfig(dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAll(JSONObject.parseObject(dto.getHeader(), Map.class));
        HttpEntity<String> entity = new HttpEntity<>(dto.getDynSentence(), headers);
        ResponseEntity<Object> exchange;
        try {
            exchange = restTemplate.exchange(dto.getApiUrl(), HttpMethod.valueOf(dto.getMethod()), entity, Object.class);
        } catch (Exception e) {
            log.error("error", e);
            throw BusinessExceptionBuilder.build(ResponseCode.DATA_SOURCE_CONNECTION_FAILED, e.getMessage());
        }
        if (exchange.getStatusCode().isError()) {
            throw BusinessExceptionBuilder.build(ResponseCode.DATA_SOURCE_CONNECTION_FAILED, exchange.getBody());
        }
        Object body = exchange.getBody();
        String jsonStr = JSONObject.toJSONString(body);
        List<JSONObject> result = new ArrayList<>();
        if (jsonStr.trim().startsWith(BusinessConstant.LEFT_BIG_BOAST) && jsonStr.trim().endsWith(BusinessConstant.RIGTH_BIG_BOAST)) {
            //JSONObject
            result.add(JSONObject.parseObject(jsonStr));
        } else if (jsonStr.trim().startsWith(BusinessConstant.LEFT_MIDDLE_BOAST) && jsonStr.trim().endsWith(BusinessConstant.RIGHT_MIDDLE_BOAST)) {
            //List
            result = JSONArray.parseArray(jsonStr, JSONObject.class);
        } else {
            result.add(new JSONObject());
        }
        return result;
    }


    // es通过api获取数据 因为restTemplate未引入，暂时注释掉
    public List<JSONObject> executeElasticsearchSql(DataSourceDto dto) {
        analysisHttpConfig(dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAll(JSONObject.parseObject(dto.getHeader(), Map.class));
        HttpEntity<String> entity = new HttpEntity<>(dto.getDynSentence(), headers);
        ResponseEntity<JSONObject> exchange;
        try {
            exchange = restTemplate.exchange(dto.getApiUrl(), HttpMethod.valueOf(dto.getMethod()), entity, JSONObject.class);
        } catch (Exception e) {
            log.error("error", e);
            throw BusinessExceptionBuilder.build(ResponseCode.DATA_SOURCE_CONNECTION_FAILED, e.getMessage());
        }
        if (exchange.getStatusCode().isError()) {
            throw BusinessExceptionBuilder.build(ResponseCode.DATA_SOURCE_CONNECTION_FAILED, exchange.getBody());
        }
        List<JSONObject> result;
        try {
            JSONObject body = exchange.getBody();
            //解析es sql数据
            if (null == body) {
                return null;
            }
            JSONArray columns = body.getJSONArray("columns");
            JSONArray rows = body.getJSONArray("rows");
            result = new ArrayList<>();
            for (int i = 0; i < rows.size(); i++) {
                JSONArray row = rows.getJSONArray(i);
                JSONObject jsonObject = new JSONObject();
                for (int j = 0; j < row.size(); j++) {
                    String name = columns.getJSONObject(j).getString("name");
                    String value = row.getString(j);
                    jsonObject.put(name, value);
                }
                result.add(jsonObject);
            }
        } catch (Exception e) {
            log.error("error", e);
            throw BusinessExceptionBuilder.build(ResponseCode.ANALYSIS_DATA_ERROR, e.getMessage());
        }
        return result;
    }

    /**
     * es通过api获取数据
     *
     * @param dto
     * @return
     */
    public void analysisHttpConfig(DataSourceDto dto) {
        JSONObject json = JSONObject.parseObject(dto.getSourceconfig());
        GaeaAssert.isFalse(json.containsKey("apiUrl"), ResponseCode.PARAM_IS_NULL, "apiUrl not empty");
        GaeaAssert.isFalse(json.containsKey("method"), ResponseCode.PARAM_IS_NULL, "method not empty");
        GaeaAssert.isFalse(json.containsKey("header"), ResponseCode.PARAM_IS_NULL, "header not empty");
        GaeaAssert.isFalse(json.containsKey("body"), ResponseCode.PARAM_IS_NULL, "body not empty");
        String apiUrl = json.getString("apiUrl");
        String method = json.getString("method");
        String header = json.getString("header");
        String body = json.getString("body");
//        //解决url中存在的动态参数
//        apiUrl = dataSetParamService.transform(dto.getContextData(), apiUrl);
//        //请求头中动态参数
//        header = dataSetParamService.transform(dto.getContextData(), header);
        dto.setApiUrl(apiUrl);
        dto.setMethod(method);
        dto.setHeader(header);
        dto.setBody(body);
    }

}
