package inks.service.sa.uts.controller;

import cn.hutool.json.XML;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.constant.CacheConstants;
import inks.common.core.constant.ConfigConstant;
import inks.common.core.domain.ApprrecPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsWxeapprPojo;
import inks.service.sa.uts.domain.pojo.UtsWxeapprrecPojo;
import inks.service.sa.uts.service.UtsWxeapprService;
import inks.service.sa.uts.service.UtsWxeapprrecService;
import inks.service.sa.uts.utils.wxutils.*;
import inks.service.sa.uts.utils.wxutils.aes.AesException;
import inks.service.sa.uts.utils.wxutils.aes.WXBizMsgCrypt;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.xml.sax.SAXException;

import javax.annotation.Resource;
import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 企业微审核(Uts_WxeAppr)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:08
 */
@RestController
@RequestMapping("S34M06B1")
@Api(tags = "S34M06B1:企业微信:审批模板")
public class S34M06B1Controller extends UtsWxeapprController {

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(S34M06B1Controller.class);
    /**
     * 服务对象
     */
    @Resource
    private UtsWxeapprService utsWxeapprService;
    /**
     * 服务对象
     */
    @Resource
    private UtsWxeapprrecService utsWxeapprrecService;
    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaConfigService saConfigService;

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<UtsWxeapprPojo>> getListByModuleCode(String code, String tid) {
        try {
            //有Tid参
            if (tid == null || tid.equals("")) {
                // 获得用户数据
                LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            List<UtsWxeapprPojo> list = this.utsWxeapprService.getListByModuleCode(code, tid);
            if (list != null) {
                for (int i = 0; i < list.size(); i++) {
                    String verifyKey = CacheConstants.APPR_CODES_KEY + list.get(i).getId();
                    ApprrecPojo apprrecPojo = new ApprrecPojo();
                    org.springframework.beans.BeanUtils.copyProperties(list.get(i), apprrecPojo);
                    apprrecPojo.setId("");
                    saRedisService.setCacheObject(verifyKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
                    list.get(i).setDatatemp(null);
                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发出审批", notes = "发出审批", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R sendapprovel(String key, String tid) {
        try {

            logger.info("-----开始发出审批-----");
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
            logger.info("企业微信 corpId:" + QyWeChat.corpId);
            QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
            logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);
            QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
            logger.info("企业微信 agentID:" + QyWeChat.agentId);


            String CachKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(CachKey, ApprrecPojo.class);
            //获取企业微信审批TOKEN
            String token = QyWeChatUtils.refreshToken("agentToken");
            logger.info("企业微信 发出审批:token:" + token);
            //设置参数
            String url = QyWeChat.approveURL.replace("{access_token}", token);
            //发送审批
            JSONObject jsonObject = HttpRequestUtil.sendPost(url, apprrecPojo.getDatatemp());

            String sp_no = jsonObject.getString("sp_no");
            if (sp_no != null) {
                logger.info("企业微信 发出审批:sp_no:" + sp_no);
                apprrecPojo.setApprsn(sp_no);
                UtsWxeapprrecPojo utsWxeapprrecPojo = new UtsWxeapprrecPojo();
                org.springframework.beans.BeanUtils.copyProperties(apprrecPojo, utsWxeapprrecPojo);
                utsWxeapprrecService.insert(utsWxeapprrecPojo);
            } else {
                logger.info("企业微信 发出审批:文本内容:" + apprrecPojo.getDatatemp());
                logger.info("企业微信 发出审批:errmsg:" + jsonObject.getString("errmsg"));
                return R.fail("企业微信 发出审批:errmsg:" + jsonObject.getString("errmsg"));
            }

            return R.ok(jsonObject);
        } catch (Exception e) {
            logger.info("企业微信 发出审批:出错:" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description GET方法用于企业微信验证回调函数
     * @date 2022/1/15
     * @param * @param null
     * @return
     */
    @GetMapping
    public String verifyURL(@RequestParam(name = "msg_signature") String signature, String timestamp, String nonce, String echostr, String tid) throws AesException {
        logger.info("-----开始签名校验-----");
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        logger.info("企业微信 corpId:" + QyWeChat.corpId);
        QyWeChat.callbacktoken = mapcfg.get(ConfigConstant.WXE_CALLBACK_TOKEN);
        logger.info("企业微信 callbacktoken:" + QyWeChat.callbacktoken);
        QyWeChat.callbackAESKey = mapcfg.get(ConfigConstant.WXE_CALLBACK_AESKEY);
        logger.info("企业微信 callbackAESKey:" + QyWeChat.callbackAESKey);
        //token和encoding_aeskey就是上诉随机获取的值，corp_id是企业id，在企业微信管理页面点击： 我的企业，拉到最下方可以看到
        WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(QyWeChat.callbacktoken.trim(), QyWeChat.callbackAESKey.trim(), QyWeChat.corpId.trim());
        String sEchoStr = wxcpt.VerifyURL(signature, timestamp, nonce, echostr);
        logger.info("-----签名校验通过-----");
        return sEchoStr;
    }

    @PostMapping
    public String callbackURL(@RequestBody InMsgPojo msg, @RequestParam(name = "msg_signature") String signature,
                              String timestamp, String nonce, String tid) throws AesException, ParserConfigurationException, IOException, SAXException {
        logger.info("-----开始审批回调-----");
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        logger.info("企业微信 corpId:" + QyWeChat.corpId);
        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
        logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);
        QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
        logger.info("企业微信 agentID:" + QyWeChat.agentId);
        logger.info("审批回调：开始计算密钥");
        String sEncryptMsg = "";
        try {
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(QyWeChat.callbacktoken.trim(), QyWeChat.callbackAESKey.trim(), QyWeChat.corpId.trim());
            String sReqData = FormatUtils.getReqData(msg);
            // logger.info("sReqData:"+sReqData);
            String sMsg = wxcpt.DecryptMsg(signature, timestamp, nonce, sReqData);
            // logger.info("sMsg:"+sMsg);
            ElementUtils element = new ElementUtils(sMsg);
            String sRespData = FormatUtils.getTextRespData("");
            sEncryptMsg = wxcpt.EncryptMsg(sRespData, timestamp, nonce);
            // logger.info("sEncryptMsg :"+sEncryptMsg );
            String jsonsMsg = XML.toJSONObject(sMsg).toString();
            logger.info("jsonsMsg :" + jsonsMsg);
            ResultXML resultXML = JSONObject.parseObject(jsonsMsg, ResultXML.class);

            //获得审批单号
            Long spno = resultXML.getXml().getApprovalInfo().getSpNo();
            logger.info("审批单号：" + spno);
            //查询审批记录
            UtsWxeapprrecPojo utsWxeapprrecPojo = utsWxeapprrecService.getEntityBySpno(spno + "", tid);
            utsWxeapprrecPojo.setCallbackdate(new Date());
            utsWxeapprrecPojo.setCallbackuuid(resultXML.getXml().getApprovalInfo().getSpRecord().get(resultXML.getXml().getApprovalInfo().getSpRecord().size() - 1).getDetails().getApprover().getUserId());
            utsWxeapprrecPojo.setCallbackmsg(resultXML.toString());
            utsWxeapprrecPojo.setModifydate(new Date());
            utsWxeapprrecPojo.setRemark(resultXML.getXml().getApprovalInfo().getSpRecord().get(resultXML.getXml().getApprovalInfo().getSpRecord().size() - 1).getDetails().getSpeech());
// 审批状态
            Integer spstatus = resultXML.getXml().getApprovalInfo().getSpStatus();
            String callbackResult;
            boolean needCallback = true;
            boolean approved = false;

            if (spstatus == 2) {
                logger.info("审批回调：审核通过");
                callbackResult = "审批通过";
                approved = true;
            } else if (spstatus == 3) {
                logger.info("审批回调：审批被驳回");
                callbackResult = "审批驳回";
            } else {
                logger.info("审批回调：审批转发，无需处理");
                return null; // 审批转发不执行后续逻辑
            }

            // 1. 写入数据库
            utsWxeapprrecPojo.setCallbackresult(callbackResult);
            utsWxeapprrecService.update(utsWxeapprrecPojo);
            logger.info("审批回调：写入数据库完成");

            // 2. 写入 Redis
            ApprrecPojo apprrecPojo = new ApprrecPojo();
            org.springframework.beans.BeanUtils.copyProperties(utsWxeapprrecPojo, apprrecPojo);
            String cacheKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setCacheObject(cacheKey, apprrecPojo, 60L * 12, TimeUnit.MINUTES);
            logger.info("审批回调：写入 Redis 完成 " + cacheKey);

            // 3. 执行回调
            String url = utsWxeapprrecPojo.getCallbackurl().replace("{key}", utsWxeapprrecPojo.getId()) + "&type=wxe";
            if (!approved) {
                url += "&approved=false";
            }
            logger.info("审批回调：开始执行回调 URL -> " + url);
            HttpRequestUtil.sendGet(url);


        } catch (Exception ex) {
            logger.info("审批出错" + ex.getMessage());
        }
        logger.info("-----结束审批回调-----");
        return sEncryptMsg;
    }


    //    key为template_id


//    @ApiOperation(value = "测试模板审批", notes = "测试模板审批", produces = "application/json")
//    @RequestMapping(value = "/testApproval", method = RequestMethod.GET)
//    public R<JSONObject> testApproval(String tid, String code) {
//        if (StringUtils.isBlank(tid)) {
//            tid = saRedisService.getLoginUser().getTenantid();
//        }
//        //获取企业微信模板和模板测试数据
//        List<UtsWxeapprPojo> utsWxeapprPojos = utsWxeapprService.getListByModuleCode(code, tid);
//        //获取企业微信参数
//        Map<String, String> mapcfg = saConfigService.getConfigAll();
//        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
//        logger.info("企业微信 corpId:" + QyWeChat.corpId);
//        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
//        logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);
//        QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
//        logger.info("企业微信 agentID:" + QyWeChat.agentId);
//        //获取企业微信TOKEN
//        String token = QyWeChatUtils.refreshToken("agentToken");
//        //设置参数
//        String url = QyWeChat.approveURL.replace("{access_token}", token);
//        //测试发送审批请求 getTestdata为真实写死数据
//        JSONObject jsonObject = HttpRequestUtil.sendPost(url, utsWxeapprPojos.get(0).getTestdata());
//        String sp_no = jsonObject.getString("sp_no");
//        if (sp_no != null) {
//            //审批发送成功
//            System.out.println("sp_no = " + sp_no);
//        }
//        return R.ok(jsonObject);
//    }
//
//
//    @ApiOperation(value = "VM模板测试模板审批", notes = "测试模板审批", produces = "application/json")
//    @RequestMapping(value = "/testApprovalVM", method = RequestMethod.GET)
//    public R<JSONObject> testApprovalVM(String tid, String code) {
//        if (StringUtils.isBlank(tid)) {
//            tid = saRedisService.getLoginUser().getTenantid();
//        }
//        //获取企业微信模板和模板测试数据
//        List<UtsWxeapprPojo> utsWxeapprPojos = utsWxeapprService.getListByModuleCode(code, tid);
//        //获取企业微信参数
//        Map<String, String> mapcfg = saConfigService.getConfigAll();
//        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
//        logger.info("企业微信 corpId:" + QyWeChat.corpId);
//        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
//        logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);
//        QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
//        logger.info("企业微信 agentID:" + QyWeChat.agentId);
//        //获取企业微信TOKEN
//        String token = QyWeChatUtils.refreshToken("agentToken");
//
//        //--------模拟销售订单主子表数据用来测试VM模板引擎
//        HashMap<Object, Object> map = new HashMap<>();
//        map.put("creatoruserid", "nanno");
//        map.put("userid", "nanno");
//        //设置template_id
//        map.put("modelcode", "3WLJ7932fjkVzAoAfDcuSVS4PKJoXDrj9tDzGUnB");
//        HashMap<String, Object> billObject = new HashMap<String, Object>();
//        //设置主表groupname,refno
//        billObject.put("groupname", "测试groupname");
//        billObject.put("refno", "测试refno2023");
//        //设置子表item
//        HashMap<String, Object> item1 = new HashMap<String, Object>() {
//            {
//                put("goodsname", "商品名1");
//                put("price", 100);
//            }
//        };
//        HashMap<String, Object> item2 = new HashMap<String, Object>() {
//            {
//                put("goodsname", "商品名2");
//                put("price", 200);
//            }
//        };
//        HashMap<String, Object> item3 = new HashMap<String, Object>() {
//            {
//                put("goodsname", "商品名3");
//                put("price", 300);
//            }
//        };
//        ArrayList<Object> lst = new ArrayList<>();
//        lst.add(item1);
//        lst.add(item2);
//        lst.add(item3);
//        billObject.put("item", lst);
//        map.put("object", billObject);
//
//        //-----创建VM数据对象
//        VelocityContext context = new VelocityContext();
//        context.put("approvePojo", map);
//        //获取VM模板 带${}
//        String str = utsWxeapprPojos.get(0).getDatatemp();
//        // 初始化并取得Velocity引擎
//        VelocityEngine ve = new VelocityEngine();
//        ve.init();
//        // 转换输出
//        StringWriter writer = new StringWriter();
//        ve.evaluate(context, writer, "", str); // 关键方法
//        //写回String
//        str = writer.toString();
//
//
//        //设置参数
//        String url = QyWeChat.approveURL.replace("{access_token}", token);
//        //测试发送审批请求 getTestdata为真实写死数据
//        //JSONObject jsonObject = HttpRequestUtil.sendPost(url, utsWxeapprPojos.get(0).getTestdata());
//        //---------------------测试发送审批请求 str为VM模板替换数据
//        JSONObject jsonObject = HttpRequestUtil.sendPost(url, str);
//        String sp_no = jsonObject.getString("sp_no");
//        if (sp_no != null) {
//            //审批发送成功
//            logger.info("审批发送成功sp_no：" + sp_no);
//        } else {
//            throw new RuntimeException("审批发送失败");
//        }
//        return R.ok(jsonObject);
//    }
//
//
//    @ApiOperation(value = "VM模板测试模板审批", notes = "测试模板审批", produces = "application/json")
//    @RequestMapping(value = "/testApprovalXSVM", method = RequestMethod.GET)
//    public R<JSONObject> testApprovalXSVM(String tid, String code) {
//        if (StringUtils.isBlank(tid)) {
//            tid = saRedisService.getLoginUser().getTenantid();
//        }
//        //获取企业微信模板和模板测试数据
//        List<UtsWxeapprPojo> utsWxeapprPojos = utsWxeapprService.getListByModuleCode(code, tid);
//        //获取企业微信参数
//        Map<String, String> mapcfg = saConfigService.getConfigAll();
//        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
//        logger.info("企业微信 corpId:" + QyWeChat.corpId);
//        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
//        logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);
//        QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
//        logger.info("企业微信 agentID:" + QyWeChat.agentId);
//        //获取企业微信TOKEN
//        String token = QyWeChatUtils.refreshToken("agentToken");
//
//        //--------模拟销售订单主子表数据用来测试VM模板引擎
//        HashMap<Object, Object> map = new HashMap<>();
//        map.put("creatoruserid", "nanno");
//        map.put("userid", "nanno");
//        //设置template_id
//        map.put("modelcode", "C4UAWELPHbJDV3qDRKfmKNZEuHQqB1EcLAGeRHM5Z");
////        HashMap<String, Object> billObject = new HashMap<String, Object>();
////        //设置主表groupname,refno
////        billObject.put("groupname", "测试groupname");
////        billObject.put("refno", "测试refno2023");
////        //设置子表item
////        HashMap<String, Object> item1 = new HashMap<String, Object>() {
////            {
////                put("goodsname", "商品名1");
////                put("price", 100);
////            }
////        };
////        HashMap<String, Object> item2 = new HashMap<String, Object>() {
////            {
////                put("goodsname", "商品名2");
////                put("price", 200);
////            }
////        };
////        HashMap<String, Object> item3 = new HashMap<String, Object>() {
////            {
////                put("goodsname", "商品名3");
////                put("price", 300);
////            }
////        };
////        ArrayList<Object> lst = new ArrayList<>();
////        lst.add(item1);
////        lst.add(item2);
////        lst.add(item3);
////        billObject.put("item", lst);
//
//        String billObject = "{\"groupuid\":\"G002\",\"groupname\":\"蒋\",\"abbreviate\":\"jyf\",\"grouplevel\":null,\"id\":\"00305375-882d-4463-9a77-f38928bb7616\",\"refno\":\"BM-2023-01-0002\",\"billtype\":\"量产订单\",\"billtitle\":\"销售单据-直接下单\",\"billdate\":\"2023-01-06T08:08:51.000+00:00\",\"groupid\":\"c54a9683-a24e-4362-8df2-374bcb2081a4\",\"custorderid\":\"111343\",\"logisticsmode\":\"\",\"logisticsport\":\"\",\"country\":\"\",\"advaamount\":0,\"salesman\":\"\",\"salesmanid\":\"\",\"taxrate\":0,\"summary\":\"备注111\",\"createby\":\"Demo\",\"createbyid\":\"fc2a723f-fc73-48a8-98ae-8d4150a5da4f\",\"createdate\":\"2023-01-06T08:12:39.000+00:00\",\"lister\":\"Demo\",\"listerid\":\"fc2a723f-fc73-48a8-98ae-8d4150a5da4f\",\"modifydate\":\"2023-02-21T05:31:01.000+00:00\",\"assessor\":\"\",\"assessorid\":\"\",\"assessdate\":\"2023-02-21T05:31:01.000+00:00\",\"billtaxamount\":1935,\"billtaxtotal\":0,\"billamount\":1935,\"billstatecode\":\"\",\"billstatedate\":\"2023-01-06T08:12:39.000+00:00\",\"billplandate\":\"2023-01-06T15:59:59.000+00:00\",\"billwkwpid\":\"\",\"billwkwpcode\":\"\",\"billwkwpname\":\"\",\"groupcode\":\"\",\"itemcount\":2,\"pickcount\":0,\"finishcount\":2,\"disannulcount\":0,\"printcount\":0,\"wkfinishcount\":0,\"payment\":\"\",\"custom1\":\"\",\"custom2\":\"\",\"custom3\":\"\",\"custom4\":\"\",\"custom5\":\"\",\"custom6\":\"\",\"custom7\":\"\",\"custom8\":\"\",\"custom9\":\"\",\"custom10\":\"\",\"deptid\":\"\",\"tenantid\":\"984a29c0-2f14-4178-93f6-ef63761976c8\",\"tenantname\":\"\",\"revision\":9,\"item\":[{\"goodsuid\":\"JYF110003nanno\",\"goodsname\":\"铠侠硬盘\",\"goodsspec\":\"1T\",\"goodsunit\":\"件\",\"partid\":\"\",\"goodsmaterial\":\"\",\"goodsphoto1\":null,\"goodscustom1\":\"\",\"goodscustom2\":\"\",\"goodscustom3\":\"\",\"goodscustom4\":\"\",\"goodscustom5\":\"\",\"goodscustom6\":\"\",\"goodscustom7\":\"\",\"goodscustom8\":\"\",\"goodscustom9\":\"\",\"goodscustom10\":\"\",\"id\":\"fde3f81d-bfeb-4caf-b9b9-376caf2441b2\",\"pid\":\"00305375-882d-4463-9a77-f38928bb7616\",\"goodsid\":\"2f44748f-df44-40ea-bde7-0f3e7d7ad36f\",\"quantity\":3,\"taxprice\":555,\"taxamount\":1665,\"itemtaxrate\":0,\"taxtotal\":0,\"price\":555,\"amount\":1665,\"itemorgdate\":\"2023-01-06T08:12:12.000+00:00\",\"itemplandate\":\"2023-01-06T08:12:12.000+00:00\",\"wkqty\":2,\"stoqty\":1,\"rownum\":0,\"remark\":\"\",\"engstatetext\":\"\",\"engstatedate\":\"2023-01-06T08:12:12.000+00:00\",\"wkstatetext\":\"\",\"wkstatedate\":\"2023-01-06T08:12:39.000+00:00\",\"busstatetext\":\"\",\"busstatedate\":\"2023-01-06T08:12:12.000+00:00\",\"buyquantity\":0,\"wkquantity\":0,\"inquantity\":0,\"pickqty\":0,\"finishqty\":3,\"outquantity\":0,\"outsecqty\":0,\"editioninfo\":\"\",\"itemcompdate\":\"2023-01-06T08:12:39.000+00:00\",\"virtualitem\":0,\"closed\":1,\"stdprice\":0,\"stdamount\":0,\"rebate\":0,\"mrpuid\":\"\",\"mrpid\":\"\",\"maxqty\":0,\"location\":\"\",\"batchno\":\"\",\"disannulmark\":0,\"wipused\":0,\"wkwpid\":\"\",\"wkwpcode\":\"\",\"wkwpname\":\"\",\"wkrownum\":0,\"ordercostuid\":\"\",\"ordercostitemid\":\"\",\"quotuid\":\"\",\"quotitemid\":\"\",\"bomtype\":1,\"bomid\":\"\",\"bomuid\":\"\",\"bomstate\":\"\",\"attributejson\":\"\",\"machtype\":\"\",\"reordermark\":0,\"matcode\":\"\",\"matused\":0,\"costitemjson\":\"\",\"costgroupjson\":\"\",\"custom1\":\"\",\"custom2\":\"\",\"custom3\":\"\",\"custom4\":\"\",\"custom5\":\"\",\"custom6\":\"\",\"custom7\":\"\",\"custom8\":\"\",\"custom9\":\"\",\"custom10\":\"\",\"custom11\":\"\",\"custom12\":\"\",\"custom13\":\"\",\"custom14\":\"\",\"custom15\":\"\",\"custom16\":\"\",\"custom17\":\"\",\"custom18\":\"\",\"tenantid\":\"984a29c0-2f14-4178-93f6-ef63761976c8\",\"revision\":5},{\"goodsuid\":\"JYF110004nanno\",\"goodsname\":\"散热风扇\",\"goodsspec\":\"ARGB\",\"goodsunit\":\"个\",\"partid\":\"\",\"goodsmaterial\":\"\",\"goodsphoto1\":null,\"goodscustom1\":\"\",\"goodscustom2\":\"\",\"goodscustom3\":\"\",\"goodscustom4\":\"\",\"goodscustom5\":\"\",\"goodscustom6\":\"\",\"goodscustom7\":\"\",\"goodscustom8\":\"\",\"goodscustom9\":\"\",\"goodscustom10\":\"\",\"id\":\"064abcd0-346f-4986-a466-7eeb68ee868d\",\"pid\":\"00305375-882d-4463-9a77-f38928bb7616\",\"goodsid\":\"ca80ed6c-f5ac-4740-a151-1ec9c54fee86\",\"quantity\":9,\"taxprice\":30,\"taxamount\":270,\"itemtaxrate\":0,\"taxtotal\":0,\"price\":30,\"amount\":270,\"itemorgdate\":\"2023-01-06T08:12:12.000+00:00\",\"itemplandate\":\"2023-01-06T08:12:12.000+00:00\",\"wkqty\":8,\"stoqty\":1,\"rownum\":1,\"remark\":\"\",\"engstatetext\":\"\",\"engstatedate\":\"2023-01-06T08:12:12.000+00:00\",\"wkstatetext\":\"\",\"wkstatedate\":\"2023-01-06T08:12:40.000+00:00\",\"busstatetext\":\"\",\"busstatedate\":\"2023-01-06T08:12:12.000+00:00\",\"buyquantity\":0,\"wkquantity\":0,\"inquantity\":0,\"pickqty\":0,\"finishqty\":9,\"outquantity\":0,\"outsecqty\":0,\"editioninfo\":\"\",\"itemcompdate\":\"2023-01-06T08:12:40.000+00:00\",\"virtualitem\":0,\"closed\":0,\"stdprice\":0,\"stdamount\":0,\"rebate\":0,\"mrpuid\":\"\",\"mrpid\":\"\",\"maxqty\":0,\"location\":\"\",\"batchno\":\"\",\"disannulmark\":0,\"wipused\":0,\"wkwpid\":\"\",\"wkwpcode\":\"\",\"wkwpname\":\"\",\"wkrownum\":0,\"ordercostuid\":\"\",\"ordercostitemid\":\"\",\"quotuid\":\"\",\"quotitemid\":\"\",\"bomtype\":0,\"bomid\":\"\",\"bomuid\":\"\",\"bomstate\":\"\",\"attributejson\":\"\",\"machtype\":\"\",\"reordermark\":0,\"matcode\":\"\",\"matused\":0,\"costitemjson\":\"\",\"costgroupjson\":\"\",\"custom1\":\"\",\"custom2\":\"\",\"custom3\":\"\",\"custom4\":\"\",\"custom5\":\"\",\"custom6\":\"\",\"custom7\":\"\",\"custom8\":\"\",\"custom9\":\"\",\"custom10\":\"\",\"custom11\":\"\",\"custom12\":\"\",\"custom13\":\"\",\"custom14\":\"\",\"custom15\":\"\",\"custom16\":\"\",\"custom17\":\"\",\"custom18\":\"\",\"tenantid\":\"984a29c0-2f14-4178-93f6-ef63761976c8\",\"revision\":4}],\"grouplink\":\"联系人蒋\",\"grouptel\":\"84325471\",\"groupadd\":\"嘉兴\"}";
//        JSONObject jsonObject1 = JSONArray.parseObject(billObject);
//        map.put("object", jsonObject1);
//
//
//        //-----创建VM数据对象
//        VelocityContext context = new VelocityContext();
//        context.put("approvePojo", map);
//        //获取VM模板 带${}
//        String str = utsWxeapprPojos.get(0).getDatatemp();
//        // 初始化并取得Velocity引擎
//        VelocityEngine ve = new VelocityEngine();
//        ve.init();
//        // 转换输出
//        StringWriter writer = new StringWriter();
//        ve.evaluate(context, writer, "", str); // 关键方法
//        //写回String
//        str = writer.toString();
//
//
//        //设置参数
//        String url = QyWeChat.approveURL.replace("{access_token}", token);
//        //测试发送审批请求 getTestdata为真实写死数据
//        //JSONObject jsonObject = HttpRequestUtil.sendPost(url, utsWxeapprPojos.get(0).getTestdata());
//        //---------------------测试发送审批请求 str为VM模板替换数据
//        JSONObject jsonObject = HttpRequestUtil.sendPost(url, str);
//        String sp_no = jsonObject.getString("sp_no");
//        if (sp_no != null) {
//            //审批发送成功
//            logger.info("审批发送成功sp_no：" + sp_no);
//        } else {
//            throw new RuntimeException("审批发送失败");
//        }
//        return R.ok(jsonObject);
//    }


}
