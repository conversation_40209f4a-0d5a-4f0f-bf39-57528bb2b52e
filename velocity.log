2025-07-23 15:24:32,952 - Initializing Velocity, Calling init()...
2025-07-23 15:24:32,952 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 15:24:32,952 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 15:24:32,952 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 15:24:32,952 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-23 15:24:32,952 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 15:24:32,952 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 15:24:32,957 - <PERSON><PERSON>oader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 15:24:32,958 - Do unicode file recognition:  false
2025-07-23 15:24:32,958 - FileResourceLoader : adding path '.'
2025-07-23 15:24:32,968 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 15:24:32,973 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 15:24:32,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 15:24:32,975 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 15:24:32,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 15:24:32,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 15:24:32,977 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 15:24:32,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 15:24:32,979 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 15:24:32,980 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 15:24:33,003 - Created '20' parsers.
2025-07-23 15:24:33,006 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 15:24:33,006 - Velocimacro : Default library not found.
2025-07-23 15:24:33,006 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 15:24:33,006 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 15:24:33,006 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 15:24:33,006 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 15:42:56,583 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 15:42:56,583 - Initializing Velocity, Calling init()...
2025-07-23 15:42:56,583 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 15:42:56,583 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 15:42:56,583 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 15:42:56,583 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 15:42:56,583 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 15:42:56,583 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 15:42:56,584 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 15:42:56,584 - Do unicode file recognition:  false
2025-07-23 15:42:56,584 - FileResourceLoader : adding path '.'
2025-07-23 15:42:56,584 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 15:42:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 15:42:56,588 - Created '20' parsers.
2025-07-23 15:42:56,589 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 15:42:56,589 - Velocimacro : Default library not found.
2025-07-23 15:42:56,589 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 15:42:56,589 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 15:42:56,589 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 15:42:56,589 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:00:25,865 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:00:25,866 - Initializing Velocity, Calling init()...
2025-07-23 16:00:25,866 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:00:25,866 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:00:25,866 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:00:25,866 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:00:25,866 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:00:25,866 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:00:25,866 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:00:25,866 - Do unicode file recognition:  false
2025-07-23 16:00:25,866 - FileResourceLoader : adding path '.'
2025-07-23 16:00:25,866 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:00:25,866 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:00:25,866 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:00:25,866 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:00:25,866 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:00:25,867 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:00:25,867 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:00:25,867 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:00:25,867 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:00:25,867 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:00:25,868 - Created '20' parsers.
2025-07-23 16:00:25,868 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:00:25,868 - Velocimacro : Default library not found.
2025-07-23 16:00:25,868 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:00:25,868 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:00:25,868 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:00:25,868 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:00:25,871 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-23 16:04:31,877 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:04:31,877 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:04:31,878 - Initializing Velocity, Calling init()...
2025-07-23 16:04:31,878 - Initializing Velocity, Calling init()...
2025-07-23 16:04:31,878 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:04:31,878 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:04:31,878 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:04:31,878 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:04:31,878 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:04:31,878 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:04:31,878 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:04:31,878 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:04:31,878 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:04:31,878 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:04:31,878 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:04:31,878 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:04:31,878 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:04:31,878 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:04:31,878 - Do unicode file recognition:  false
2025-07-23 16:04:31,878 - Do unicode file recognition:  false
2025-07-23 16:04:31,878 - FileResourceLoader : adding path '.'
2025-07-23 16:04:31,878 - FileResourceLoader : adding path '.'
2025-07-23 16:04:31,879 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:04:31,879 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:04:31,879 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:04:31,880 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:04:31,880 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:04:31,880 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:04:31,880 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:04:31,880 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:04:31,880 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:04:31,880 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:04:31,880 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:04:31,882 - Created '20' parsers.
2025-07-23 16:04:31,882 - Created '20' parsers.
2025-07-23 16:04:31,882 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:04:31,882 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:04:31,882 - Velocimacro : Default library not found.
2025-07-23 16:04:31,882 - Velocimacro : Default library not found.
2025-07-23 16:04:31,882 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:04:31,882 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:04:31,882 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:04:31,882 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:04:31,882 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:04:31,882 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:04:31,882 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:04:31,882 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:07:56,582 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:07:56,582 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:07:56,582 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:07:56,583 - Initializing Velocity, Calling init()...
2025-07-23 16:07:56,583 - Initializing Velocity, Calling init()...
2025-07-23 16:07:56,583 - Initializing Velocity, Calling init()...
2025-07-23 16:07:56,583 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:07:56,583 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:07:56,583 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:07:56,583 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:07:56,583 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:07:56,583 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:07:56,583 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:07:56,583 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:07:56,583 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:07:56,583 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:07:56,583 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:07:56,583 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:07:56,583 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:07:56,583 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:07:56,583 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:07:56,584 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:07:56,584 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:07:56,584 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:07:56,584 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:07:56,584 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:07:56,584 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:07:56,584 - Do unicode file recognition:  false
2025-07-23 16:07:56,584 - Do unicode file recognition:  false
2025-07-23 16:07:56,584 - Do unicode file recognition:  false
2025-07-23 16:07:56,584 - FileResourceLoader : adding path '.'
2025-07-23 16:07:56,584 - FileResourceLoader : adding path '.'
2025-07-23 16:07:56,584 - FileResourceLoader : adding path '.'
2025-07-23 16:07:56,584 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:07:56,584 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:07:56,584 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:07:56,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:07:56,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:07:56,588 - Created '20' parsers.
2025-07-23 16:07:56,588 - Created '20' parsers.
2025-07-23 16:07:56,588 - Created '20' parsers.
2025-07-23 16:07:56,588 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:07:56,588 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:07:56,588 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:07:56,588 - Velocimacro : Default library not found.
2025-07-23 16:07:56,588 - Velocimacro : Default library not found.
2025-07-23 16:07:56,588 - Velocimacro : Default library not found.
2025-07-23 16:07:56,588 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:07:56,588 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:07:56,588 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:07:56,588 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:07:56,588 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:07:56,588 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:07:56,588 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:07:56,588 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:07:56,588 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:07:56,588 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:07:56,588 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:07:56,588 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:08:38,887 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:08:38,887 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:08:38,887 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:08:38,887 - Log4JLogChute initialized using file 'velocity.log'
2025-07-23 16:08:38,888 - Initializing Velocity, Calling init()...
2025-07-23 16:08:38,888 - Initializing Velocity, Calling init()...
2025-07-23 16:08:38,888 - Initializing Velocity, Calling init()...
2025-07-23 16:08:38,888 - Initializing Velocity, Calling init()...
2025-07-23 16:08:38,888 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:08:38,888 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:08:38,888 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:08:38,888 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:08:38,888 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:08:38,888 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:08:38,888 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:08:38,888 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:08:38,888 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:08:38,888 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:08:38,888 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:08:38,888 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:08:38,888 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:08:38,888 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:08:38,888 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:08:38,888 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-23 16:08:38,888 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:08:38,888 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:08:38,888 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:08:38,888 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:08:38,888 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:08:38,888 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:08:38,888 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:08:38,888 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:08:38,888 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:08:38,888 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:08:38,888 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:08:38,888 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:08:38,889 - Do unicode file recognition:  false
2025-07-23 16:08:38,889 - Do unicode file recognition:  false
2025-07-23 16:08:38,889 - Do unicode file recognition:  false
2025-07-23 16:08:38,889 - Do unicode file recognition:  false
2025-07-23 16:08:38,889 - FileResourceLoader : adding path '.'
2025-07-23 16:08:38,889 - FileResourceLoader : adding path '.'
2025-07-23 16:08:38,889 - FileResourceLoader : adding path '.'
2025-07-23 16:08:38,889 - FileResourceLoader : adding path '.'
2025-07-23 16:08:38,889 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:08:38,889 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:08:38,889 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:08:38,889 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:08:38,889 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:08:38,890 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:08:38,891 - Created '20' parsers.
2025-07-23 16:08:38,891 - Created '20' parsers.
2025-07-23 16:08:38,891 - Created '20' parsers.
2025-07-23 16:08:38,891 - Created '20' parsers.
2025-07-23 16:08:38,892 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:08:38,892 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:08:38,892 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:08:38,892 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:08:38,892 - Velocimacro : Default library not found.
2025-07-23 16:08:38,892 - Velocimacro : Default library not found.
2025-07-23 16:08:38,892 - Velocimacro : Default library not found.
2025-07-23 16:08:38,892 - Velocimacro : Default library not found.
2025-07-23 16:08:38,892 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:08:38,892 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:08:38,892 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:08:38,892 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:08:38,892 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:08:38,892 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:08:38,892 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:08:38,892 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:08:38,892 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:08:38,892 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:08:38,892 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:08:38,892 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:08:38,892 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:08:38,892 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:08:38,892 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:08:38,892 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 11:29:21,215 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 11:29:21,215 - Initializing Velocity, Calling init()...
2025-07-25 11:29:21,215 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 11:29:21,215 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 11:29:21,215 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 11:29:21,215 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-25 11:29:21,215 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 11:29:21,215 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 11:29:21,223 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 11:29:21,224 - Do unicode file recognition:  false
2025-07-25 11:29:21,224 - FileResourceLoader : adding path '.'
2025-07-25 11:29:21,241 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 11:29:21,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 11:29:21,245 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 11:29:21,245 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 11:29:21,246 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 11:29:21,247 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 11:29:21,248 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 11:29:21,250 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 11:29:21,251 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 11:29:21,252 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 11:29:21,273 - Created '20' parsers.
2025-07-25 11:29:21,277 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 11:29:21,277 - Velocimacro : Default library not found.
2025-07-25 11:29:21,277 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 11:29:21,277 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 11:29:21,277 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 11:29:21,277 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 12:37:50,003 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 12:37:50,003 - Initializing Velocity, Calling init()...
2025-07-25 12:37:50,003 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 12:37:50,003 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 12:37:50,004 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 12:37:50,004 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 12:37:50,004 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:37:50,004 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:37:50,004 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 12:37:50,004 - Do unicode file recognition:  false
2025-07-25 12:37:50,004 - FileResourceLoader : adding path '.'
2025-07-25 12:37:50,004 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 12:37:50,004 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 12:37:50,004 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 12:37:50,004 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 12:37:50,004 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 12:37:50,004 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 12:37:50,004 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 12:37:50,004 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 12:37:50,005 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 12:37:50,005 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 12:37:50,006 - Created '20' parsers.
2025-07-25 12:37:50,006 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 12:37:50,006 - Velocimacro : Default library not found.
2025-07-25 12:37:50,006 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 12:37:50,006 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 12:37:50,006 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 12:37:50,006 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 12:42:29,308 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 12:42:29,308 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 12:42:29,309 - Initializing Velocity, Calling init()...
2025-07-25 12:42:29,309 - Initializing Velocity, Calling init()...
2025-07-25 12:42:29,309 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 12:42:29,309 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 12:42:29,309 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 12:42:29,309 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 12:42:29,309 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 12:42:29,309 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 12:42:29,309 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 12:42:29,309 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 12:42:29,309 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:42:29,309 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:42:29,309 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:42:29,309 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:42:29,309 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 12:42:29,309 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 12:42:29,309 - Do unicode file recognition:  false
2025-07-25 12:42:29,309 - Do unicode file recognition:  false
2025-07-25 12:42:29,309 - FileResourceLoader : adding path '.'
2025-07-25 12:42:29,309 - FileResourceLoader : adding path '.'
2025-07-25 12:42:29,309 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 12:42:29,309 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 12:42:29,309 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 12:42:29,309 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 12:42:29,309 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 12:42:29,309 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 12:42:29,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 12:42:29,311 - Created '20' parsers.
2025-07-25 12:42:29,311 - Created '20' parsers.
2025-07-25 12:42:29,311 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 12:42:29,311 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 12:42:29,311 - Velocimacro : Default library not found.
2025-07-25 12:42:29,311 - Velocimacro : Default library not found.
2025-07-25 12:42:29,311 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 12:42:29,311 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 12:42:29,311 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 12:42:29,311 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 12:42:29,311 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 12:42:29,311 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 12:42:29,311 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 12:42:29,311 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 12:43:22,976 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 12:43:22,976 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 12:43:22,976 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 12:43:22,976 - Initializing Velocity, Calling init()...
2025-07-25 12:43:22,976 - Initializing Velocity, Calling init()...
2025-07-25 12:43:22,976 - Initializing Velocity, Calling init()...
2025-07-25 12:43:22,976 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 12:43:22,976 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 12:43:22,976 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 12:43:22,976 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 12:43:22,976 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 12:43:22,976 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 12:43:22,976 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 12:43:22,976 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 12:43:22,976 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 12:43:22,976 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 12:43:22,976 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 12:43:22,976 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 12:43:22,976 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:43:22,976 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:43:22,976 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:43:22,976 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:43:22,976 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:43:22,976 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 12:43:22,976 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 12:43:22,976 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 12:43:22,976 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 12:43:22,976 - Do unicode file recognition:  false
2025-07-25 12:43:22,976 - Do unicode file recognition:  false
2025-07-25 12:43:22,976 - Do unicode file recognition:  false
2025-07-25 12:43:22,976 - FileResourceLoader : adding path '.'
2025-07-25 12:43:22,976 - FileResourceLoader : adding path '.'
2025-07-25 12:43:22,976 - FileResourceLoader : adding path '.'
2025-07-25 12:43:22,976 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 12:43:22,976 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 12:43:22,976 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 12:43:22,978 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 12:43:22,979 - Created '20' parsers.
2025-07-25 12:43:22,979 - Created '20' parsers.
2025-07-25 12:43:22,979 - Created '20' parsers.
2025-07-25 12:43:22,979 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 12:43:22,979 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 12:43:22,979 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 12:43:22,980 - Velocimacro : Default library not found.
2025-07-25 12:43:22,980 - Velocimacro : Default library not found.
2025-07-25 12:43:22,980 - Velocimacro : Default library not found.
2025-07-25 12:43:22,980 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 12:43:22,980 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 12:43:22,980 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 12:43:22,980 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 12:43:22,980 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 12:43:22,980 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 12:43:22,980 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 12:43:22,980 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 12:43:22,980 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 12:43:22,980 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 12:43:22,980 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 12:43:22,980 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:18:35,638 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:18:35,638 - Initializing Velocity, Calling init()...
2025-07-25 13:18:35,638 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:18:35,638 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:18:35,638 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:18:35,638 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-25 13:18:35,638 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:18:35,638 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:18:35,641 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:18:35,642 - Do unicode file recognition:  false
2025-07-25 13:18:35,642 - FileResourceLoader : adding path '.'
2025-07-25 13:18:35,651 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:18:35,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:18:35,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:18:35,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:18:35,656 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:18:35,656 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:18:35,656 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:18:35,657 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:18:35,658 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:18:35,659 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:18:35,672 - Created '20' parsers.
2025-07-25 13:18:35,674 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:18:35,674 - Velocimacro : Default library not found.
2025-07-25 13:18:35,674 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:18:35,674 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:18:35,674 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:18:35,674 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:19:20,562 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:19:20,562 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:19:20,563 - Initializing Velocity, Calling init()...
2025-07-25 13:19:20,563 - Initializing Velocity, Calling init()...
2025-07-25 13:19:20,563 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:19:20,563 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:19:20,563 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:19:20,563 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:19:20,563 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:19:20,563 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:19:20,564 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 13:19:20,564 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 13:19:20,564 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:19:20,564 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:19:20,564 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:19:20,564 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:19:20,564 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:19:20,564 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:19:20,564 - Do unicode file recognition:  false
2025-07-25 13:19:20,564 - Do unicode file recognition:  false
2025-07-25 13:19:20,564 - FileResourceLoader : adding path '.'
2025-07-25 13:19:20,564 - FileResourceLoader : adding path '.'
2025-07-25 13:19:20,564 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:19:20,564 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:19:20,564 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:19:20,564 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:19:20,564 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:19:20,564 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:19:20,565 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:19:20,567 - Created '20' parsers.
2025-07-25 13:19:20,567 - Created '20' parsers.
2025-07-25 13:19:20,567 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:19:20,567 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:19:20,567 - Velocimacro : Default library not found.
2025-07-25 13:19:20,567 - Velocimacro : Default library not found.
2025-07-25 13:19:20,567 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:19:20,567 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:19:20,567 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:19:20,567 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:19:20,567 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:19:20,567 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:19:20,567 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:19:20,567 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:26:42,811 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:26:42,811 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:26:42,811 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:26:42,812 - Initializing Velocity, Calling init()...
2025-07-25 13:26:42,812 - Initializing Velocity, Calling init()...
2025-07-25 13:26:42,812 - Initializing Velocity, Calling init()...
2025-07-25 13:26:42,812 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:26:42,812 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:26:42,812 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:26:42,812 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:26:42,812 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:26:42,812 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:26:42,812 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:26:42,812 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:26:42,812 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:26:42,812 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 13:26:42,812 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 13:26:42,812 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 13:26:42,812 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:26:42,812 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:26:42,812 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:26:42,812 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:26:42,812 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:26:42,812 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:26:42,813 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:26:42,813 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:26:42,813 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:26:42,813 - Do unicode file recognition:  false
2025-07-25 13:26:42,813 - Do unicode file recognition:  false
2025-07-25 13:26:42,813 - Do unicode file recognition:  false
2025-07-25 13:26:42,813 - FileResourceLoader : adding path '.'
2025-07-25 13:26:42,813 - FileResourceLoader : adding path '.'
2025-07-25 13:26:42,813 - FileResourceLoader : adding path '.'
2025-07-25 13:26:42,813 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:26:42,813 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:26:42,813 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:26:42,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:26:42,815 - Created '20' parsers.
2025-07-25 13:26:42,815 - Created '20' parsers.
2025-07-25 13:26:42,815 - Created '20' parsers.
2025-07-25 13:26:42,815 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:26:42,815 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:26:42,815 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:26:42,815 - Velocimacro : Default library not found.
2025-07-25 13:26:42,815 - Velocimacro : Default library not found.
2025-07-25 13:26:42,815 - Velocimacro : Default library not found.
2025-07-25 13:26:42,816 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:26:42,816 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:26:42,816 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:26:42,816 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:26:42,816 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:26:42,816 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:26:42,816 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:26:42,816 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:26:42,816 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:26:42,816 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:26:42,816 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:26:42,816 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:44:07,098 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:44:07,099 - Initializing Velocity, Calling init()...
2025-07-25 13:44:07,099 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:44:07,099 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:44:07,099 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:44:07,099 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-25 13:44:07,099 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:44:07,099 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:44:07,102 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:44:07,103 - Do unicode file recognition:  false
2025-07-25 13:44:07,103 - FileResourceLoader : adding path '.'
2025-07-25 13:44:07,110 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:44:07,112 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:44:07,113 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:44:07,114 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:44:07,114 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:44:07,115 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:44:07,115 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:44:07,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:44:07,117 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:44:07,117 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:44:07,128 - Created '20' parsers.
2025-07-25 13:44:07,130 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:44:07,130 - Velocimacro : Default library not found.
2025-07-25 13:44:07,130 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:44:07,130 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:44:07,130 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:44:07,130 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:44:15,926 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:44:15,926 - Log4JLogChute initialized using file 'velocity.log'
2025-07-25 13:44:15,926 - Initializing Velocity, Calling init()...
2025-07-25 13:44:15,926 - Initializing Velocity, Calling init()...
2025-07-25 13:44:15,926 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:44:15,926 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-25 13:44:15,926 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:44:15,926 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-25 13:44:15,926 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:44:15,926 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-25 13:44:15,926 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 13:44:15,926 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-25 13:44:15,926 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:44:15,926 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:44:15,926 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:44:15,926 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-25 13:44:15,926 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:44:15,926 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-25 13:44:15,927 - Do unicode file recognition:  false
2025-07-25 13:44:15,927 - Do unicode file recognition:  false
2025-07-25 13:44:15,927 - FileResourceLoader : adding path '.'
2025-07-25 13:44:15,927 - FileResourceLoader : adding path '.'
2025-07-25 13:44:15,927 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:44:15,927 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:44:15,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-25 13:44:15,928 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:44:15,928 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-25 13:44:15,928 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:44:15,928 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-25 13:44:15,928 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:44:15,928 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-25 13:44:15,928 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:44:15,928 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-25 13:44:15,928 - Created '20' parsers.
2025-07-25 13:44:15,928 - Created '20' parsers.
2025-07-25 13:44:15,929 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:44:15,929 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-25 13:44:15,929 - Velocimacro : Default library not found.
2025-07-25 13:44:15,929 - Velocimacro : Default library not found.
2025-07-25 13:44:15,929 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:44:15,929 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-25 13:44:15,929 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:44:15,929 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-25 13:44:15,929 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:44:15,929 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-25 13:44:15,929 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-25 13:44:15,929 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 08:47:02,195 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 08:47:02,196 - Initializing Velocity, Calling init()...
2025-07-29 08:47:02,196 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 08:47:02,196 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 08:47:02,196 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 08:47:02,196 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-29 08:47:02,196 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 08:47:02,196 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 08:47:02,200 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 08:47:02,200 - Do unicode file recognition:  false
2025-07-29 08:47:02,200 - FileResourceLoader : adding path '.'
2025-07-29 08:47:02,211 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 08:47:02,213 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 08:47:02,214 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 08:47:02,215 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 08:47:02,216 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 08:47:02,216 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 08:47:02,217 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 08:47:02,218 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 08:47:02,219 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 08:47:02,219 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 08:47:02,232 - Created '20' parsers.
2025-07-29 08:47:02,234 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 08:47:02,234 - Velocimacro : Default library not found.
2025-07-29 08:47:02,234 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 08:47:02,235 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 08:47:02,235 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 08:47:02,235 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 10:07:10,781 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 10:07:10,781 - Initializing Velocity, Calling init()...
2025-07-29 10:07:10,781 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 10:07:10,781 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 10:07:10,781 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 10:07:10,781 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-29 10:07:10,781 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 10:07:10,782 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 10:07:10,785 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 10:07:10,786 - Do unicode file recognition:  false
2025-07-29 10:07:10,786 - FileResourceLoader : adding path '.'
2025-07-29 10:07:10,795 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 10:07:10,797 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 10:07:10,798 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 10:07:10,798 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 10:07:10,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 10:07:10,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 10:07:10,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 10:07:10,801 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 10:07:10,802 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 10:07:10,802 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 10:07:10,813 - Created '20' parsers.
2025-07-29 10:07:10,815 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 10:07:10,815 - Velocimacro : Default library not found.
2025-07-29 10:07:10,815 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 10:07:10,815 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 10:07:10,815 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 10:07:10,815 - Velocimacro : autoload off : VM system will not automatically reload global library macros
