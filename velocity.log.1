2025-07-01 11:16:50,525 - Initializing Velocity, Calling init()...
2025-07-01 11:16:50,525 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:16:50,525 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:16:50,525 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:16:50,525 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-01 11:16:50,525 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:16:50,525 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:16:50,541 - <PERSON><PERSON>oader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:16:50,544 - Do unicode file recognition:  false
2025-07-01 11:16:50,544 - FileResourceLoader : adding path '.'
2025-07-01 11:16:50,615 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:16:50,623 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:16:50,626 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:16:50,628 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:16:50,629 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:16:50,631 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:16:50,633 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:16:50,636 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:16:50,638 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:16:50,640 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:16:50,752 - Created '20' parsers.
2025-07-01 11:16:50,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:16:50,758 - Velocimacro : Default library not found.
2025-07-01 11:16:50,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:16:50,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:16:50,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:16:50,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:16:50,816 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:17:19,863 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:17:19,864 - Initializing Velocity, Calling init()...
2025-07-01 11:17:19,864 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:17:19,864 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:17:19,864 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:17:19,864 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:17:19,864 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:17:19,864 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:17:19,864 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:17:19,864 - Do unicode file recognition:  false
2025-07-01 11:17:19,864 - FileResourceLoader : adding path '.'
2025-07-01 11:17:19,864 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:17:19,865 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:17:19,866 - Created '20' parsers.
2025-07-01 11:17:19,866 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:17:19,866 - Velocimacro : Default library not found.
2025-07-01 11:17:19,866 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:17:19,866 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:17:19,866 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:17:19,866 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:17:19,867 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:17:24,433 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:17:24,434 - Initializing Velocity, Calling init()...
2025-07-01 11:17:24,434 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:17:24,434 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:17:24,434 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:17:24,435 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:17:24,435 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:17:24,435 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:17:24,435 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:17:24,435 - Do unicode file recognition:  false
2025-07-01 11:17:24,435 - FileResourceLoader : adding path '.'
2025-07-01 11:17:24,435 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:17:24,436 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:17:24,436 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:17:24,436 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:17:24,437 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:17:24,437 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:17:24,437 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:17:24,437 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:17:24,437 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:17:24,437 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:17:24,438 - Created '20' parsers.
2025-07-01 11:17:24,438 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:17:24,438 - Velocimacro : Default library not found.
2025-07-01 11:17:24,439 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:17:24,439 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:17:24,439 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:17:24,439 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:17:24,440 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:18:21,167 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:18:21,168 - Initializing Velocity, Calling init()...
2025-07-01 11:18:21,169 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:18:21,169 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:18:21,169 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:18:21,170 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:18:21,170 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:18:21,170 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:18:21,171 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:18:21,172 - Do unicode file recognition:  false
2025-07-01 11:18:21,172 - FileResourceLoader : adding path '.'
2025-07-01 11:18:21,172 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:18:21,176 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:18:21,177 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:18:21,178 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:18:21,180 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:18:21,181 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:18:21,182 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:18:21,182 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:18:21,183 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:18:21,183 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:18:21,186 - Created '20' parsers.
2025-07-01 11:18:21,187 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:18:21,187 - Velocimacro : Default library not found.
2025-07-01 11:18:21,188 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:18:21,188 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:18:21,188 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:18:21,189 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:18:21,199 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:18:50,488 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:18:50,489 - Initializing Velocity, Calling init()...
2025-07-01 11:18:50,489 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:18:50,489 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:18:50,489 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:18:50,489 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:18:50,489 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:18:50,489 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:18:50,489 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:18:50,489 - Do unicode file recognition:  false
2025-07-01 11:18:50,490 - FileResourceLoader : adding path '.'
2025-07-01 11:18:50,490 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:18:50,490 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:18:50,490 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:18:50,490 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:18:50,490 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:18:50,490 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:18:50,491 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:18:50,491 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:18:50,491 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:18:50,491 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:18:50,491 - Created '20' parsers.
2025-07-01 11:18:50,492 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:18:50,492 - Velocimacro : Default library not found.
2025-07-01 11:18:50,492 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:18:50,492 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:18:50,492 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:18:50,493 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:18:50,493 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:02,187 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:02,188 - Initializing Velocity, Calling init()...
2025-07-01 11:19:02,188 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:02,188 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:02,188 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:02,189 - Do unicode file recognition:  false
2025-07-01 11:19:02,189 - FileResourceLoader : adding path '.'
2025-07-01 11:19:02,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:02,190 - Created '20' parsers.
2025-07-01 11:19:02,190 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:02,191 - Velocimacro : Default library not found.
2025-07-01 11:19:02,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:02,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:02,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:02,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:02,191 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:12,568 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:12,569 - Initializing Velocity, Calling init()...
2025-07-01 11:19:12,569 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:12,569 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:12,569 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:12,569 - Do unicode file recognition:  false
2025-07-01 11:19:12,570 - FileResourceLoader : adding path '.'
2025-07-01 11:19:12,570 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:12,572 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:12,574 - Created '20' parsers.
2025-07-01 11:19:12,574 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:12,574 - Velocimacro : Default library not found.
2025-07-01 11:19:12,575 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:12,575 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:12,575 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:12,575 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:12,576 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:21:55,314 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:21:55,316 - Initializing Velocity, Calling init()...
2025-07-01 11:21:55,316 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:21:55,316 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:21:55,316 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:21:55,316 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:21:55,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:21:55,317 - Do unicode file recognition:  false
2025-07-01 11:21:55,317 - FileResourceLoader : adding path '.'
2025-07-01 11:21:55,317 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:21:55,321 - Created '20' parsers.
2025-07-01 11:21:55,321 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:21:55,322 - Velocimacro : Default library not found.
2025-07-01 11:21:55,322 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:21:55,322 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:21:55,322 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:21:55,322 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:21:55,324 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
reated '20' parsers.
2025-07-01 11:18:50,491 - Created '20' parsers.
2025-07-01 11:18:50,491 - Created '20' parsers.
2025-07-01 11:18:50,492 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:18:50,492 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:18:50,492 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:18:50,492 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:18:50,492 - Velocimacro : Default library not found.
2025-07-01 11:18:50,492 - Velocimacro : Default library not found.
2025-07-01 11:18:50,492 - Velocimacro : Default library not found.
2025-07-01 11:18:50,492 - Velocimacro : Default library not found.
2025-07-01 11:18:50,492 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:18:50,492 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:18:50,492 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:18:50,492 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:18:50,492 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:18:50,492 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:18:50,492 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:18:50,492 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:18:50,492 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:18:50,492 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:18:50,492 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:18:50,492 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:18:50,493 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:18:50,493 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:18:50,493 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:18:50,493 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:18:50,493 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:18:50,493 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:18:50,493 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:18:50,493 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:02,187 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:02,187 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:02,187 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:02,187 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:02,187 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:02,188 - Initializing Velocity, Calling init()...
2025-07-01 11:19:02,188 - Initializing Velocity, Calling init()...
2025-07-01 11:19:02,188 - Initializing Velocity, Calling init()...
2025-07-01 11:19:02,188 - Initializing Velocity, Calling init()...
2025-07-01 11:19:02,188 - Initializing Velocity, Calling init()...
2025-07-01 11:19:02,188 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:02,188 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:02,188 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:02,188 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:02,188 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:02,188 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:02,188 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:02,188 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:02,188 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:02,188 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:02,188 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:02,188 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:02,188 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:02,188 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:02,188 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,188 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:02,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:02,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:02,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:02,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:02,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:02,189 - Do unicode file recognition:  false
2025-07-01 11:19:02,189 - Do unicode file recognition:  false
2025-07-01 11:19:02,189 - Do unicode file recognition:  false
2025-07-01 11:19:02,189 - Do unicode file recognition:  false
2025-07-01 11:19:02,189 - Do unicode file recognition:  false
2025-07-01 11:19:02,189 - FileResourceLoader : adding path '.'
2025-07-01 11:19:02,189 - FileResourceLoader : adding path '.'
2025-07-01 11:19:02,189 - FileResourceLoader : adding path '.'
2025-07-01 11:19:02,189 - FileResourceLoader : adding path '.'
2025-07-01 11:19:02,189 - FileResourceLoader : adding path '.'
2025-07-01 11:19:02,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:02,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:02,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:02,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:02,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:02,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:02,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:02,190 - Created '20' parsers.
2025-07-01 11:19:02,190 - Created '20' parsers.
2025-07-01 11:19:02,190 - Created '20' parsers.
2025-07-01 11:19:02,190 - Created '20' parsers.
2025-07-01 11:19:02,190 - Created '20' parsers.
2025-07-01 11:19:02,190 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:02,190 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:02,190 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:02,190 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:02,190 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:02,191 - Velocimacro : Default library not found.
2025-07-01 11:19:02,191 - Velocimacro : Default library not found.
2025-07-01 11:19:02,191 - Velocimacro : Default library not found.
2025-07-01 11:19:02,191 - Velocimacro : Default library not found.
2025-07-01 11:19:02,191 - Velocimacro : Default library not found.
2025-07-01 11:19:02,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:02,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:02,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:02,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:02,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:02,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:02,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:02,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:02,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:02,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:02,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:02,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:02,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:02,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:02,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:02,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:02,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:02,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:02,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:02,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:02,191 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:02,191 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:02,191 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:02,191 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:02,191 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:12,568 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:12,568 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:12,568 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:12,568 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:12,568 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:12,568 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:19:12,569 - Initializing Velocity, Calling init()...
2025-07-01 11:19:12,569 - Initializing Velocity, Calling init()...
2025-07-01 11:19:12,569 - Initializing Velocity, Calling init()...
2025-07-01 11:19:12,569 - Initializing Velocity, Calling init()...
2025-07-01 11:19:12,569 - Initializing Velocity, Calling init()...
2025-07-01 11:19:12,569 - Initializing Velocity, Calling init()...
2025-07-01 11:19:12,569 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:12,569 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:12,569 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:12,569 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:12,569 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:12,569 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:19:12,569 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:12,569 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:12,569 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:12,569 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:12,569 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:12,569 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:19:12,569 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:12,569 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:12,569 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:12,569 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:12,569 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:12,569 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:19:12,569 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:12,569 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:12,569 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:12,569 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:12,569 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:12,569 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:19:12,569 - Do unicode file recognition:  false
2025-07-01 11:19:12,569 - Do unicode file recognition:  false
2025-07-01 11:19:12,569 - Do unicode file recognition:  false
2025-07-01 11:19:12,569 - Do unicode file recognition:  false
2025-07-01 11:19:12,569 - Do unicode file recognition:  false
2025-07-01 11:19:12,569 - Do unicode file recognition:  false
2025-07-01 11:19:12,570 - FileResourceLoader : adding path '.'
2025-07-01 11:19:12,570 - FileResourceLoader : adding path '.'
2025-07-01 11:19:12,570 - FileResourceLoader : adding path '.'
2025-07-01 11:19:12,570 - FileResourceLoader : adding path '.'
2025-07-01 11:19:12,570 - FileResourceLoader : adding path '.'
2025-07-01 11:19:12,570 - FileResourceLoader : adding path '.'
2025-07-01 11:19:12,570 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:12,570 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:12,570 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:12,570 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:12,570 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:12,570 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:12,571 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:19:12,572 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:12,572 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:12,572 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:12,572 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:12,572 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:12,572 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:19:12,574 - Created '20' parsers.
2025-07-01 11:19:12,574 - Created '20' parsers.
2025-07-01 11:19:12,574 - Created '20' parsers.
2025-07-01 11:19:12,574 - Created '20' parsers.
2025-07-01 11:19:12,574 - Created '20' parsers.
2025-07-01 11:19:12,574 - Created '20' parsers.
2025-07-01 11:19:12,574 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:12,574 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:12,574 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:12,574 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:12,574 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:12,574 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:19:12,574 - Velocimacro : Default library not found.
2025-07-01 11:19:12,574 - Velocimacro : Default library not found.
2025-07-01 11:19:12,574 - Velocimacro : Default library not found.
2025-07-01 11:19:12,574 - Velocimacro : Default library not found.
2025-07-01 11:19:12,574 - Velocimacro : Default library not found.
2025-07-01 11:19:12,574 - Velocimacro : Default library not found.
2025-07-01 11:19:12,575 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:12,575 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:12,575 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:12,575 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:12,575 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:12,575 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:19:12,575 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:12,575 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:12,575 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:12,575 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:12,575 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:12,575 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:19:12,575 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:12,575 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:12,575 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:12,575 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:12,575 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:12,575 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:19:12,575 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:12,575 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:12,575 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:12,575 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:12,575 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:12,575 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:19:12,576 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:12,576 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:12,576 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:12,576 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:12,576 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:19:12,576 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:21:55,314 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:21:55,314 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:21:55,314 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:21:55,314 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:21:55,314 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:21:55,314 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:21:55,314 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:21:55,316 - Initializing Velocity, Calling init()...
2025-07-01 11:21:55,316 - Initializing Velocity, Calling init()...
2025-07-01 11:21:55,316 - Initializing Velocity, Calling init()...
2025-07-01 11:21:55,316 - Initializing Velocity, Calling init()...
2025-07-01 11:21:55,316 - Initializing Velocity, Calling init()...
2025-07-01 11:21:55,316 - Initializing Velocity, Calling init()...
2025-07-01 11:21:55,316 - Initializing Velocity, Calling init()...
2025-07-01 11:21:55,316 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:21:55,316 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:21:55,316 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:21:55,316 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:21:55,316 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:21:55,316 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:21:55,316 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:21:55,316 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:21:55,316 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:21:55,316 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:21:55,316 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:21:55,316 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:21:55,316 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:21:55,316 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:21:55,316 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:21:55,316 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:21:55,316 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:21:55,316 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:21:55,316 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:21:55,316 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:21:55,316 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:21:55,316 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:21:55,316 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:21:55,316 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:21:55,316 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:21:55,316 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:21:55,316 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:21:55,316 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:21:55,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:21:55,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:21:55,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:21:55,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:21:55,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:21:55,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:21:55,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:21:55,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:21:55,317 - Do unicode file recognition:  false
2025-07-01 11:21:55,317 - Do unicode file recognition:  false
2025-07-01 11:21:55,317 - Do unicode file recognition:  false
2025-07-01 11:21:55,317 - Do unicode file recognition:  false
2025-07-01 11:21:55,317 - Do unicode file recognition:  false
2025-07-01 11:21:55,317 - Do unicode file recognition:  false
2025-07-01 11:21:55,317 - Do unicode file recognition:  false
2025-07-01 11:21:55,317 - FileResourceLoader : adding path '.'
2025-07-01 11:21:55,317 - FileResourceLoader : adding path '.'
2025-07-01 11:21:55,317 - FileResourceLoader : adding path '.'
2025-07-01 11:21:55,317 - FileResourceLoader : adding path '.'
2025-07-01 11:21:55,317 - FileResourceLoader : adding path '.'
2025-07-01 11:21:55,317 - FileResourceLoader : adding path '.'
2025-07-01 11:21:55,317 - FileResourceLoader : adding path '.'
2025-07-01 11:21:55,317 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:21:55,317 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:21:55,317 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:21:55,317 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:21:55,317 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:21:55,317 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:21:55,317 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:21:55,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:21:55,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:21:55,321 - Created '20' parsers.
2025-07-01 11:21:55,321 - Created '20' parsers.
2025-07-01 11:21:55,321 - Created '20' parsers.
2025-07-01 11:21:55,321 - Created '20' parsers.
2025-07-01 11:21:55,321 - Created '20' parsers.
2025-07-01 11:21:55,321 - Created '20' parsers.
2025-07-01 11:21:55,321 - Created '20' parsers.
2025-07-01 11:21:55,321 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:21:55,321 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:21:55,321 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:21:55,321 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:21:55,321 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:21:55,321 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:21:55,321 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:21:55,322 - Velocimacro : Default library not found.
2025-07-01 11:21:55,322 - Velocimacro : Default library not found.
2025-07-01 11:21:55,322 - Velocimacro : Default library not found.
2025-07-01 11:21:55,322 - Velocimacro : Default library not found.
2025-07-01 11:21:55,322 - Velocimacro : Default library not found.
2025-07-01 11:21:55,322 - Velocimacro : Default library not found.
2025-07-01 11:21:55,322 - Velocimacro : Default library not found.
2025-07-01 11:21:55,322 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:21:55,322 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:21:55,322 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:21:55,322 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:21:55,322 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:21:55,322 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:21:55,322 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:21:55,322 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:21:55,322 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:21:55,322 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:21:55,322 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:21:55,322 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:21:55,322 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:21:55,322 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:21:55,322 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:21:55,322 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:21:55,322 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:21:55,322 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:21:55,322 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:21:55,322 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:21:55,322 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:21:55,322 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:21:55,322 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:21:55,322 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:21:55,322 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:21:55,322 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:21:55,322 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:21:55,322 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:21:55,324 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:21:55,324 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:21:55,324 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:21:55,324 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:21:55,324 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:21:55,324 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:21:55,324 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,261 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Initializing Velocity, Calling init()...
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - Do unicode file recognition:  false
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - FileResourceLoader : adding path '.'
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,264 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Created '20' parsers.
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : Default library not found.
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,265 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:23:02,266 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,577 - Log4JLogChute initialized using file 'velocity.log'
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Initializing Velocity, Calling init()...
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,581 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,582 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - Do unicode file recognition:  false
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - FileResourceLoader : adding path '.'
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Created '20' parsers.
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : Default library not found.
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,586 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-01 11:24:06,587 - Null reference [template 'VelocityUtils', line 1, column 30] : $data.businesstitle cannot be resolved.
2025-07-23 15:24:32,950 - Log4JLogChute initialized using file 'velocity.log'
